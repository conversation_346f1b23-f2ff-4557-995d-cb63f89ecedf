using System;

class Program
{
    static void Main()
    {
        string password = "admin123";
        
        Console.WriteLine($"Generating BCrypt hash for password: {password}");
        
        // 生成BCrypt哈希
        string hash = BCrypt.Net.BCrypt.HashPassword(password);
        
        Console.WriteLine($"Generated hash: {hash}");
        
        // 验证哈希
        bool isValid = BCrypt.Net.BCrypt.Verify(password, hash);
        Console.WriteLine($"Verification result: {isValid}");
        
        // 生成几个不同的哈希值供选择
        Console.WriteLine("\nAdditional hashes:");
        for (int i = 0; i < 3; i++)
        {
            string newHash = BCrypt.Net.BCrypt.HashPassword(password);
            bool newIsValid = BCrypt.Net.BCrypt.Verify(password, newHash);
            Console.WriteLine($"Hash {i + 1}: {newHash}");
            Console.WriteLine($"Valid: {newIsValid}");
            Console.WriteLine();
        }
    }
}

using WatermarkCore.Models;
using WatermarkCore.Models.DTOs;

namespace WatermarkCore.Services
{
    /// <summary>
    /// 认证服务接口
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginRequest">登录请求</param>
        /// <returns>登录响应</returns>
        Task<LoginResponseDto?> LoginAsync(LoginRequestDto loginRequest);

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="registerRequest">注册请求</param>
        /// <param name="createdById">创建者ID（管理员创建子账户时使用）</param>
        /// <returns>用户信息</returns>
        Task<UserDto?> RegisterAsync(RegisterRequestDto registerRequest, string? createdById = null);

        /// <summary>
        /// 生成JWT令牌
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>JWT令牌</returns>
        string GenerateJwtToken(User user);

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <param name="hashedPassword">哈希密码</param>
        /// <returns>是否匹配</returns>
        bool VerifyPassword(string password, string hashedPassword);

        /// <summary>
        /// 哈希密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <returns>哈希密码</returns>
        string HashPassword(string password);

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<UserDto?> GetUserByIdAsync(string userId);

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        Task<UserDto?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// 更新用户最后登录时间
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        Task UpdateLastLoginAsync(string userId);

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="changePasswordRequest">修改密码请求</param>
        /// <returns>是否成功</returns>
        Task<bool> ChangePasswordAsync(string userId, ChangePasswordRequestDto changePasswordRequest);

        /// <summary>
        /// 更新用户权限（仅管理员可操作）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionsRequest">权限请求</param>
        /// <param name="operatorId">操作者ID</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserPermissionsAsync(string userId, UpdateUserPermissionsRequestDto permissionsRequest, string operatorId);

        /// <summary>
        /// 获取用户列表（仅管理员可操作）
        /// </summary>
        /// <param name="operatorId">操作者ID</param>
        /// <returns>用户列表</returns>
        Task<List<UserDto>> GetUsersAsync(string operatorId);

        /// <summary>
        /// 删除用户（仅管理员可操作）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="operatorId">操作者ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteUserAsync(string userId, string operatorId);
    }
}

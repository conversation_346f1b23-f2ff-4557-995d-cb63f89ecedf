using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using WatermarkCore.Data;
using WatermarkCore.Models;
using WatermarkCore.Models.DTOs;
using WatermarkCore.Middleware;

namespace WatermarkCore.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // 需要登录才能访问
    public class FoldersController : ControllerBase
    {
        private readonly WatermarkDbContext _context;
        private readonly ILogger<FoldersController> _logger;

        public FoldersController(WatermarkDbContext context, ILogger<FoldersController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有文件夹（树形结构）
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<FolderDto>>> GetFolders()
        {
            try
            {
                var folders = await _context.Folders
                    .Include(f => f.Children)
                    .Include(f => f.Files)
                    .OrderBy(f => f.Level)
                    .ThenBy(f => f.Name)
                    .ToListAsync();

                var folderDtos = folders.Select(MapToDto).ToList();
                return Ok(folderDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件夹列表失败");
                return StatusCode(500, "获取文件夹列表失败");
            }
        }

        /// <summary>
        /// 获取文件夹树形结构
        /// </summary>
        [HttpGet("tree")]
        public async Task<ActionResult<List<FolderTreeNodeDto>>> GetFolderTree()
        {
            try
            {
                var folders = await _context.Folders
                    .Include(f => f.Files)
                    .OrderBy(f => f.Level)
                    .ThenBy(f => f.Name)
                    .ToListAsync();

                var tree = BuildFolderTree(folders);
                return Ok(tree);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件夹树失败");
                return StatusCode(500, "获取文件夹树失败");
            }
        }

        /// <summary>
        /// 根据ID获取文件夹
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<FolderDto>> GetFolder(string id)
        {
            try
            {
                var folder = await _context.Folders
                    .Include(f => f.Children)
                    .Include(f => f.Files)
                    .FirstOrDefaultAsync(f => f.Id == id);

                if (folder == null)
                {
                    return NotFound("文件夹不存在");
                }

                return Ok(MapToDto(folder));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件夹失败: {FolderId}", id);
                return StatusCode(500, "获取文件夹失败");
            }
        }

        /// <summary>
        /// 创建文件夹
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<FolderDto>> CreateFolder([FromBody] CreateFolderDto createDto)
        {
            try
            {
                // 获取当前用户ID
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }
                // 处理ParentId，如果为空或"root"，则设为null（表示根目录下）
                var parentId = string.IsNullOrEmpty(createDto.ParentId) || createDto.ParentId == "root" ? null : createDto.ParentId;

                // 验证父文件夹是否存在
                Folder? parentFolder = null;
                if (!string.IsNullOrEmpty(parentId))
                {
                    parentFolder = await _context.Folders.FindAsync(parentId);
                    if (parentFolder == null)
                    {
                        return BadRequest("父文件夹不存在");
                    }
                }
                else
                {
                    // 如果是根目录，查找根文件夹
                    parentFolder = await _context.Folders.FirstOrDefaultAsync(f => f.Id == "root");
                }

                // 检查同级文件夹名称是否重复
                var duplicateExists = await _context.Folders
                    .AnyAsync(f => f.ParentId == parentId && f.Name == createDto.Name);
                if (duplicateExists)
                {
                    return BadRequest("同级目录下已存在同名文件夹");
                }

                // 计算路径和层级
                var level = parentFolder?.Level + 1 ?? 0;
                var path = parentFolder != null ? $"{parentFolder.Path.TrimEnd('/')}/{createDto.Name}" : $"/{createDto.Name}";

                var folder = new Folder
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = createDto.Name,
                    ParentId = parentId,
                    Path = path,
                    Level = level,
                    Description = createDto.Description,
                    CreatedById = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Folders.Add(folder);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetFolder), new { id = folder.Id }, MapToDto(folder));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建文件夹失败");
                return StatusCode(500, "创建文件夹失败");
            }
        }

        /// <summary>
        /// 更新文件夹
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<FolderDto>> UpdateFolder(string id, [FromBody] UpdateFolderDto updateDto)
        {
            try
            {
                var folder = await _context.Folders.FindAsync(id);
                if (folder == null)
                {
                    return NotFound("文件夹不存在");
                }

                if (folder.IsSystem)
                {
                    return BadRequest("系统文件夹不能修改");
                }

                // 检查同级文件夹名称是否重复
                if (folder.Name != updateDto.Name)
                {
                    var duplicateExists = await _context.Folders
                        .AnyAsync(f => f.ParentId == folder.ParentId && f.Name == updateDto.Name && f.Id != id);
                    if (duplicateExists)
                    {
                        return BadRequest("同级目录下已存在同名文件夹");
                    }
                }

                folder.Name = updateDto.Name;
                folder.Description = updateDto.Description;
                folder.UpdatedAt = DateTime.UtcNow;

                // 如果名称改变，需要更新路径
                if (folder.Name != updateDto.Name)
                {
                    await UpdateFolderPath(folder);
                }

                await _context.SaveChangesAsync();

                return Ok(MapToDto(folder));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新文件夹失败: {FolderId}", id);
                return StatusCode(500, "更新文件夹失败");
            }
        }

        /// <summary>
        /// 删除文件夹
        /// </summary>
        [HttpDelete("{id}")]
        [Permission(Permissions.CanDeleteFiles)]
        public async Task<ActionResult> DeleteFolder(string id, [FromQuery] bool recursive = false)
        {
            try
            {
                var folder = await _context.Folders
                    .Include(f => f.Children)
                    .Include(f => f.Files)
                    .FirstOrDefaultAsync(f => f.Id == id);

                if (folder == null)
                {
                    return NotFound("文件夹不存在");
                }

                if (folder.IsSystem)
                {
                    return BadRequest("系统文件夹不能删除");
                }

                // 检查是否有子文件夹或文件
                var hasChildren = await _context.Folders.AnyAsync(f => f.ParentId == id);
                var hasFiles = await _context.Files.AnyAsync(f => f.FolderId == id);

                if ((hasChildren || hasFiles) && !recursive)
                {
                    return BadRequest("文件夹不为空，请使用递归删除或先清空文件夹");
                }

                // 递归删除
                if (recursive)
                {
                    await DeleteFolderRecursive(id);
                }
                else
                {
                    _context.Folders.Remove(folder);
                }

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除文件夹失败: {FolderId}", id);
                return StatusCode(500, "删除文件夹失败");
            }
        }

        /// <summary>
        /// 递归删除文件夹及其所有内容
        /// </summary>
        private async Task DeleteFolderRecursive(string folderId)
        {
            // 获取所有子文件夹
            var childFolders = await _context.Folders
                .Where(f => f.ParentId == folderId)
                .ToListAsync();

            // 递归删除子文件夹
            foreach (var childFolder in childFolders)
            {
                await DeleteFolderRecursive(childFolder.Id);
            }

            // 获取文件夹中的所有文件
            var files = await _context.Files
                .Where(f => f.FolderId == folderId)
                .ToListAsync();

            // 删除物理文件
            foreach (var file in files)
            {
                try
                {
                    var fullPath = Path.Combine("wwwroot/uploads", file.StoragePath);
                    if (System.IO.File.Exists(fullPath))
                    {
                        System.IO.File.Delete(fullPath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除物理文件失败: {FilePath}", file.StoragePath);
                }
            }

            // 删除数据库中的文件记录
            _context.Files.RemoveRange(files);

            // 删除文件夹
            var folder = await _context.Folders.FindAsync(folderId);
            if (folder != null)
            {
                _context.Folders.Remove(folder);
            }
        }

        /// <summary>
        /// 重命名文件夹
        /// </summary>
        [HttpPut("{id}/rename")]
        public async Task<ActionResult<FolderDto>> RenameFolder(string id, [FromBody] RenameFolderDto renameDto)
        {
            try
            {
                var folder = await _context.Folders.FindAsync(id);
                if (folder == null)
                {
                    return NotFound("文件夹不存在");
                }

                if (folder.IsSystem)
                {
                    return BadRequest("系统文件夹不能重命名");
                }

                // 检查同级文件夹名称是否重复
                var duplicateExists = await _context.Folders
                    .AnyAsync(f => f.ParentId == folder.ParentId && f.Name == renameDto.NewName && f.Id != id);
                if (duplicateExists)
                {
                    return BadRequest("同级目录下已存在同名文件夹");
                }

                var oldName = folder.Name;
                folder.Name = renameDto.NewName;
                folder.UpdatedAt = DateTime.UtcNow;

                // 更新路径
                await UpdateFolderPath(folder);

                await _context.SaveChangesAsync();

                _logger.LogInformation("文件夹重命名成功: {OldName} -> {NewName}", oldName, renameDto.NewName);
                return Ok(MapToDto(folder));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名文件夹失败: {FolderId}", id);
                return StatusCode(500, "重命名文件夹失败");
            }
        }

        /// <summary>
        /// 移动文件夹
        /// </summary>
        [HttpPut("{id}/move")]
        public async Task<ActionResult<FolderDto>> MoveFolder(string id, [FromBody] MoveFolderDto moveDto)
        {
            try
            {
                var folder = await _context.Folders.FindAsync(id);
                if (folder == null)
                {
                    return NotFound("文件夹不存在");
                }

                if (folder.IsSystem)
                {
                    return BadRequest("系统文件夹不能移动");
                }

                // 验证目标父文件夹
                if (!string.IsNullOrEmpty(moveDto.TargetParentId))
                {
                    var targetParent = await _context.Folders.FindAsync(moveDto.TargetParentId);
                    if (targetParent == null)
                    {
                        return BadRequest("目标父文件夹不存在");
                    }

                    // 检查是否移动到自己的子文件夹
                    if (await IsDescendant(moveDto.TargetParentId, id))
                    {
                        return BadRequest("不能移动到自己的子文件夹");
                    }
                }

                // 检查目标位置是否有同名文件夹
                var duplicateExists = await _context.Folders
                    .AnyAsync(f => f.ParentId == moveDto.TargetParentId && f.Name == folder.Name && f.Id != id);
                if (duplicateExists)
                {
                    return BadRequest("目标位置已存在同名文件夹");
                }

                folder.ParentId = moveDto.TargetParentId;
                folder.UpdatedAt = DateTime.UtcNow;

                await UpdateFolderPath(folder);
                await _context.SaveChangesAsync();

                return Ok(MapToDto(folder));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动文件夹失败: {FolderId}", id);
                return StatusCode(500, "移动文件夹失败");
            }
        }

        #region 私有方法

        private static FolderDto MapToDto(Folder folder)
        {
            return new FolderDto
            {
                Id = folder.Id,
                Name = folder.Name,
                ParentId = folder.ParentId,
                Path = folder.Path,
                Level = folder.Level,
                IsSystem = folder.IsSystem,
                Description = folder.Description,
                CreatedAt = folder.CreatedAt,
                UpdatedAt = folder.UpdatedAt,
                Children = folder.Children?.Select(MapToDto).ToList() ?? new List<FolderDto>(),
                Files = folder.Files?.Select(MapFileToDto).ToList() ?? new List<FileItemDto>()
            };
        }

        private static FileItemDto MapFileToDto(FileItem file)
        {
            return new FileItemDto
            {
                Id = file.Id,
                Name = file.Name,
                OriginalName = file.OriginalName,
                FileType = file.FileType,
                MimeType = file.MimeType,
                Size = file.Size,
                Extension = file.Extension,
                FolderId = file.FolderId,
                Description = file.Description,
                IsWatermarked = file.IsWatermarked,
                WatermarkedAt = file.WatermarkedAt,
                Status = file.Status,
                Tags = file.Tags,
                Metadata = file.Metadata,
                CreatedAt = file.CreatedAt,
                UpdatedAt = file.UpdatedAt
            };
        }

        private List<FolderTreeNodeDto> BuildFolderTree(List<Folder> folders)
        {
            var folderDict = folders.ToDictionary(f => f.Id, f => new FolderTreeNodeDto
            {
                Id = f.Id,
                Label = f.Name,
                Type = "folder",
                Children = new List<FolderTreeNodeDto>()
            });

            // 添加文件节点
            foreach (var folder in folders)
            {
                if (folderDict.TryGetValue(folder.Id, out var folderNode))
                {
                    foreach (var file in folder.Files)
                    {
                        folderNode.Children.Add(new FolderTreeNodeDto
                        {
                            Id = file.Id,
                            Label = file.Name,
                            Type = file.FileType,
                            File = MapFileToDto(file)
                        });
                    }
                }
            }

            // 构建树形结构
            var rootNodes = new List<FolderTreeNodeDto>();
            foreach (var folder in folders)
            {
                if (folderDict.TryGetValue(folder.Id, out var currentNode))
                {
                    // 根文件夹或ParentId为null的文件夹作为根节点
                    if (string.IsNullOrEmpty(folder.ParentId) || folder.Id == "root")
                    {
                        rootNodes.Add(currentNode);
                    }
                    else if (folderDict.TryGetValue(folder.ParentId, out var parentNode))
                    {
                        parentNode.Children.Insert(0, currentNode); // 文件夹在前
                    }
                }
            }

            return rootNodes;
        }

        private async Task UpdateFolderPath(Folder folder)
        {
            var parentFolder = await _context.Folders.FindAsync(folder.ParentId);
            folder.Path = parentFolder != null ? $"{parentFolder.Path.TrimEnd('/')}/{folder.Name}" : $"/{folder.Name}";
            folder.Level = parentFolder?.Level + 1 ?? 0;

            // 递归更新子文件夹路径
            var children = await _context.Folders.Where(f => f.ParentId == folder.Id).ToListAsync();
            foreach (var child in children)
            {
                await UpdateFolderPath(child);
            }
        }

        private async Task<bool> IsDescendant(string ancestorId, string descendantId)
        {
            var current = await _context.Folders.FindAsync(descendantId);
            while (current != null && !string.IsNullOrEmpty(current.ParentId))
            {
                if (current.ParentId == ancestorId)
                {
                    return true;
                }
                current = await _context.Folders.FindAsync(current.ParentId);
            }
            return false;
        }

        #endregion
    }
}

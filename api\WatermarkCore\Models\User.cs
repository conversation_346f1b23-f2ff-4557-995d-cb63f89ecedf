using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WatermarkCore.Models
{
    /// <summary>
    /// 用户实体类
    /// </summary>
    [Table("Users")]
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        [StringLength(36)]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 用户名（登录用）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [Required]
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码哈希
        /// </summary>
        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [StringLength(100)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 用户角色
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Role { get; set; } = UserRoles.User;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 是否可以删除文件（仅对普通用户有效）
        /// </summary>
        public bool CanDeleteFiles { get; set; } = false;

        /// <summary>
        /// 是否可以下载源文件（仅对普通用户有效）
        /// </summary>
        public bool CanDownloadOriginalFiles { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 创建者ID（管理员创建子账户时使用）
        /// </summary>
        [StringLength(36)]
        public string? CreatedById { get; set; }

        /// <summary>
        /// 创建者导航属性
        /// </summary>
        [ForeignKey(nameof(CreatedById))]
        public virtual User? CreatedBy { get; set; }

        /// <summary>
        /// 该用户创建的子账户
        /// </summary>
        public virtual ICollection<User> CreatedUsers { get; set; } = new List<User>();

        /// <summary>
        /// 用户上传的文件
        /// </summary>
        public virtual ICollection<FileItem> Files { get; set; } = new List<FileItem>();

        /// <summary>
        /// 用户创建的文件夹
        /// </summary>
        public virtual ICollection<Folder> Folders { get; set; } = new List<Folder>();
    }

    /// <summary>
    /// 用户角色常量
    /// </summary>
    public static class UserRoles
    {
        /// <summary>
        /// 管理员
        /// </summary>
        public const string Admin = "Admin";

        /// <summary>
        /// 普通用户
        /// </summary>
        public const string User = "User";
    }
}

-- 检查和修复管理员用户的 SQL 脚本
USE WatermarkSys;

-- 查看当前用户表中的数据
SELECT 'Current Users in database:' as Info;
SELECT Id, Username, Email, DisplayName, Role, IsActive, PasswordHash FROM Users;

-- 删除现有的admin用户（如果存在）
DELETE FROM Users WHERE Username = 'admin';

-- 重新插入管理员用户，使用正确的BCrypt哈希
-- 密码 "admin123" 的BCrypt哈希值 (使用BCrypt工作因子11)
INSERT INTO Users (
    Id,
    Username,
    Email,
    PasswordHash,
    DisplayName,
    Role,
    IsActive,
    CanDeleteFiles,
    CanDownloadOriginalFiles,
    CreatedAt,
    UpdatedAt,
    CreatedById
) VALUES (
    'admin-user-id',
    'admin',
    '<EMAIL>',
    '$2a$11$N8qNjqJvtIy8.rEXkSo2/.92E4YsVqeQVFQH5/Lv5Hs5Hs5Hs5Hs5O',
    '系统管理员',
    'Admin',
    1,
    1,
    1,
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00',
    NULL
);

-- 验证插入结果
SELECT 'Admin user after insert:' as Info;
SELECT Id, Username, Email, DisplayName, Role, IsActive, 
       SUBSTRING(PasswordHash, 1, 20) as PasswordHashPreview 
FROM Users WHERE Username = 'admin';

-- 显示所有用户
SELECT 'All users:' as Info;
SELECT Username, Email, Role, IsActive FROM Users;

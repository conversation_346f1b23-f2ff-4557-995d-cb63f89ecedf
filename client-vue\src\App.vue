<script setup>
import { ref, onMounted } from 'vue'
import { QuestionFilled, User, Lock, SwitchButton, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import HelpDialog from './components/HelpDialog.vue'
import ChangePasswordDialog from './components/ChangePasswordDialog.vue'
import { useFileStore } from './stores/fileStore'
import { useAuthStore } from './stores/authStore'

const fileStore = useFileStore()
const authStore = useAuthStore()
const showHelp = ref(false)
const showChangePassword = ref(false)

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await authStore.logout()
    ElMessage.success('已退出登录')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('登出失败:', error)
      ElMessage.error('登出失败')
    }
  }
}

// 初始化数据
onMounted(async () => {
  // 初始化认证状态
  await authStore.initAuth()

  // 如果已登录，初始化文件数据
  if (authStore.isAuthenticated) {
    await fileStore.initializeData()
  }
})
</script>

<template>
  <div class="app">
    <header class="app-header">
      <div class="header-content">
        <div class="header-text">
          <h1>水印系统</h1>
          <p>支持图片、PDF、Word文档的水印添加</p>
        </div>

        <div class="header-actions">
          <!-- 用户信息 -->
          <div class="user-info" v-if="authStore.user">
            <el-dropdown trigger="click">
              <div class="user-dropdown">
                <el-tag :type="authStore.isAdmin ? 'danger' : 'primary'" size="small">
                  {{ authStore.isAdmin ? '管理员' : '普通用户' }}
                </el-tag>
                <span class="username">{{ authStore.displayName }}</span>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="showChangePassword = true">
                    <el-icon><Lock /></el-icon>
                    修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <router-link to="/users" v-if="authStore.isAdmin">
              <el-button type="success" plain>
                <el-icon><User /></el-icon>
                用户管理
              </el-button>
            </router-link>

            <el-button type="primary" plain @click="showHelp = true">
              <el-icon><QuestionFilled /></el-icon>
              使用帮助
            </el-button>
          </div>
        </div>
      </div>
    </header>

    <main class="app-main">
      <router-view />
    </main>

    <HelpDialog v-model:visible="showHelp" />

    <!-- 修改密码对话框 -->
    <ChangePasswordDialog v-model="showChangePassword" />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f5f5f5;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-text {
  text-align: left;
}

.header-text h1 {
  font-size: 2rem;
  margin-bottom: 8px;
}

.header-text p {
  opacity: 0.9;
  font-size: 1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  color: white;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-dropdown:hover {
  background: rgba(255, 255, 255, 0.2);
}

.user-dropdown .username {
  font-weight: 500;
  font-size: 14px;
}

.dropdown-icon {
  font-size: 12px;
  transition: transform 0.2s;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}



/* Element Plus 样式覆盖 */
.el-tree-node__content {
  height: 40px;
}

.el-upload-dragger {
  width: 100%;
}
</style>

using System.ComponentModel.DataAnnotations;

namespace WatermarkCore.Models.DTOs
{
    /// <summary>
    /// 登录请求DTO
    /// </summary>
    public class LoginRequestDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 登录响应DTO
    /// </summary>
    public class LoginResponseDto
    {
        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 令牌类型
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// 过期时间（Unix时间戳）
        /// </summary>
        public long ExpiresAt { get; set; }

        /// <summary>
        /// 用户信息
        /// </summary>
        public UserDto User { get; set; } = null!;
    }

    /// <summary>
    /// 注册请求DTO
    /// </summary>
    public class RegisterRequestDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-50个字符之间")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 确认密码
        /// </summary>
        [Required(ErrorMessage = "确认密码不能为空")]
        [Compare("Password", ErrorMessage = "两次输入的密码不一致")]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [StringLength(100, ErrorMessage = "显示名称不能超过100个字符")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 用户角色（仅管理员可设置）
        /// </summary>
        public string? Role { get; set; }
    }

    /// <summary>
    /// 用户DTO
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 用户角色
        /// </summary>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 是否可以删除文件
        /// </summary>
        public bool CanDeleteFiles { get; set; }

        /// <summary>
        /// 是否可以下载源文件
        /// </summary>
        public bool CanDownloadOriginalFiles { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        public string? CreatedById { get; set; }
    }

    /// <summary>
    /// 修改密码请求DTO
    /// </summary>
    public class ChangePasswordRequestDto
    {
        /// <summary>
        /// 当前密码
        /// </summary>
        [Required(ErrorMessage = "当前密码不能为空")]
        public string CurrentPassword { get; set; } = string.Empty;

        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// 确认新密码
        /// </summary>
        [Required(ErrorMessage = "确认新密码不能为空")]
        [Compare("NewPassword", ErrorMessage = "两次输入的新密码不一致")]
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新用户权限请求DTO
    /// </summary>
    public class UpdateUserPermissionsRequestDto
    {
        /// <summary>
        /// 是否可以删除文件
        /// </summary>
        public bool CanDeleteFiles { get; set; }

        /// <summary>
        /// 是否可以下载源文件
        /// </summary>
        public bool CanDownloadOriginalFiles { get; set; }
    }
}

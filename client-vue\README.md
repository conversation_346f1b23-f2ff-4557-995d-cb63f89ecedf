# 水印系统 (Watermarking System)

一个纯前端的水印系统，支持对图片、PDF和Word文档添加水印。

## 功能特性

- 📁 **文件管理**: 支持文件上传、文件夹创建和分类管理
- 🖼️ **图片水印**: 支持JPG、PNG、GIF等格式的图片水印添加
- 📄 **PDF水印**: 支持PDF文档的文字和图片水印
- 📝 **Word水印**: 支持Word文档(.docx)的水印处理
- 🎨 **水印定制**: 支持文字水印和图片水印，可调整位置、大小、透明度、旋转角度等
- 💾 **文件导出**: 处理后的文件可直接下载

## 支持的文件格式

- **图片**: JPG, JPEG, PNG, GIF, BMP, WebP
- **PDF**: PDF文档
- **Word**: DOC, DOCX文档

## 技术栈

- **前端框架**: Vue 3 + Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **文件处理**:
  - PDF-lib (PDF处理)
  - docx (Word文档处理)
  - Canvas API (图片处理)
  - file-saver (文件下载)

## 项目设置

### 安装依赖

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

### 预览构建结果

```sh
npm run preview
```

## 使用说明

### 1. 文件管理

- 点击"上传文件"按钮选择要处理的文件
- 可以创建文件夹对文件进行分类管理
- 支持拖拽上传

### 2. 水印设置

- **文字水印**: 输入水印文字，调整字体大小、颜色、透明度
- **图片水印**: 上传水印图片，调整大小和透明度
- **位置设置**: 选择水印在文档中的位置（九宫格布局）
- **旋转角度**: 调整水印的旋转角度(-45°到45°)

### 3. 预览和导出

- 对于图片文件，可以实时预览水印效果
- 对于PDF和Word文件，显示占位符预览
- 点击"应用水印"按钮处理文件并自动下载

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── FileManager.vue  # 文件管理组件
│   └── WatermarkEditor.vue # 水印编辑组件
├── stores/             # Pinia状态管理
│   └── fileStore.js    # 文件状态管理
├── utils/              # 工具函数
│   └── watermarkUtils.js # 水印处理工具
├── App.vue             # 主应用组件
└── main.js             # 应用入口
```

## 注意事项

1. **浏览器兼容性**: 需要现代浏览器支持Canvas API和File API
2. **文件大小限制**: 大文件处理可能需要较长时间，建议文件大小控制在合理范围内
3. **Word文档处理**: 由于docx库的限制，Word文档的水印功能相对简单
4. **安全性**: 所有文件处理都在客户端进行，不会上传到服务器

## 开发说明

### 添加新的文件类型支持

1. 在 `utils/watermarkUtils.js` 中的 `FileTypeDetector` 类添加新的文件类型检测
2. 创建对应的水印处理器类
3. 在 `WatermarkEditor.vue` 中添加对应的处理逻辑

### 自定义水印样式

可以在 `WatermarkEditor.vue` 中修改水印配置选项，添加更多的样式设置。

## 许可证

MIT License

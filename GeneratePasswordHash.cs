using System;

class Program
{
    static void Main()
    {
        string password = "admin123";
        
        // 使用BCrypt生成哈希
        string hash = BCrypt.Net.BCrypt.HashPassword(password);
        
        Console.WriteLine($"Password: {password}");
        Console.WriteLine($"BCrypt Hash: {hash}");
        
        // 验证哈希
        bool isValid = BCrypt.Net.BCrypt.Verify(password, hash);
        Console.WriteLine($"Verification: {isValid}");
        
        // 测试几个不同的哈希值
        Console.WriteLine("\nTesting different hashes:");
        for (int i = 0; i < 3; i++)
        {
            string newHash = BCrypt.Net.BCrypt.HashPassword(password);
            bool newIsValid = BCrypt.Net.BCrypt.Verify(password, newHash);
            Console.WriteLine($"Hash {i + 1}: {newHash} - Valid: {newIsValid}");
        }
    }
}

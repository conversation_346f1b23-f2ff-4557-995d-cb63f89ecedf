-- 修复 Folders 表结构
USE WatermarkSys;

-- 查看当前 Folders 表结构
SELECT 'Current Folders table structure:' as Info;
DESCRIBE Folders;

-- 查看当前 Folders 表数据
SELECT 'Current Folders data:' as Info;
SELECT * FROM Folders;

-- 添加缺失的 CreatedById 字段
ALTER TABLE Folders 
ADD COLUMN CreatedById varchar(36) NOT NULL DEFAULT 'admin-user-id';

-- 添加外键约束
ALTER TABLE Folders 
ADD CONSTRAINT FK_Folders_Users_CreatedById 
FOREIGN KEY (CreatedById) REFERENCES Users(Id) ON DELETE RESTRICT;

-- 添加索引
CREATE INDEX IX_Folders_CreatedById ON Folders(CreatedById);

-- 检查是否存在根文件夹，如果不存在则创建
INSERT IGNORE INTO Folders (
    Id, 
    Name, 
    ParentId, 
    Path, 
    Level, 
    IsSystem, 
    Description, 
    CreatedById, 
    CreatedAt, 
    UpdatedAt
) VALUES (
    'root',
    '我的文件',
    NULL,
    '/',
    0,
    1,
    '系统根文件夹',
    'admin-user-id',
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00'
);

-- 验证修复结果
SELECT 'Folders table after fix:' as Info;
DESCRIBE Folders;

SELECT 'Folders data after fix:' as Info;
SELECT Id, Name, ParentId, Path, Level, IsSystem, CreatedById FROM Folders;

namespace WatermarkCore.Services
{
    /// <summary>
    /// 文件存储服务接口
    /// </summary>
    public interface IFileStorageService
    {
        /// <summary>
        /// 验证文件
        /// </summary>
        Task<FileValidationResult> ValidateFileAsync(IFormFile file);

        /// <summary>
        /// 保存文件
        /// </summary>
        Task<FileSaveResult> SaveFileAsync(IFormFile file, string fileName);

        /// <summary>
        /// 获取文件流
        /// </summary>
        Task<Stream?> GetFileStreamAsync(string filePath);

        /// <summary>
        /// 删除文件
        /// </summary>
        Task<bool> DeleteFileAsync(string filePath);

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        Task<bool> FileExistsAsync(string filePath);

        /// <summary>
        /// 获取文件大小
        /// </summary>
        Task<long> GetFileSizeAsync(string filePath);

        /// <summary>
        /// 计算文件哈希值
        /// </summary>
        Task<string> CalculateFileHashAsync(IFormFile file);
    }

    /// <summary>
    /// 文件验证结果
    /// </summary>
    public class FileValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 文件保存结果
    /// </summary>
    public class FileSaveResult
    {
        public bool Success { get; set; }
        public string? FilePath { get; set; }
        public string? FileHash { get; set; }
        public string? ErrorMessage { get; set; }
    }
}

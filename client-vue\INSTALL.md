# 安装指南

## 系统要求

- Node.js 20.19.0 或更高版本
- npm 或 yarn 包管理器
- 现代浏览器（支持 ES6+ 和 Canvas API）

## 安装步骤

### 1. 克隆或下载项目

如果你有 Git：
```bash
git clone <repository-url>
cd watermarking-system
```

### 2. 安装依赖

由于可能遇到权限问题，建议按以下顺序安装：

```bash
# 首先安装基础依赖
npm install

# 如果遇到权限问题，可以尝试：
npm install --no-optional
```

### 3. 手动安装必要的依赖（如果自动安装失败）

```bash
# 核心依赖
npm install vue@^3.5.18
npm install vue-router@^4.5.1
npm install pinia@^3.0.3

# UI 组件库
npm install element-plus@^2.8.8
npm install @element-plus/icons-vue@^2.3.1

# 文件处理库
npm install pdf-lib@^1.17.1
npm install docx@^8.5.0
npm install mammoth@^1.6.0
npm install file-saver@^2.0.5
npm install jszip@^3.10.1
```

### 4. 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:5173` 启动。

### 5. 构建生产版本

```bash
npm run build
```

构建文件将生成在 `dist` 目录中。

## 故障排除

### 权限问题

如果遇到 npm 权限问题：

1. **Windows 用户**：
   - 以管理员身份运行命令提示符或 PowerShell
   - 或者配置 npm 使用不同的目录：
     ```bash
     npm config set prefix %APPDATA%\npm
     ```

2. **macOS/Linux 用户**：
   - 使用 nvm 管理 Node.js 版本
   - 或者使用 sudo（不推荐）

### 依赖安装失败

如果某些依赖安装失败，可以尝试：

```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

### 浏览器兼容性问题

确保使用以下浏览器的最新版本：
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发环境配置

### VS Code 推荐插件

- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier

### 环境变量

创建 `.env.local` 文件（可选）：
```
VITE_APP_TITLE=水印系统
VITE_APP_VERSION=1.0.0
```

## 部署

### 静态部署

构建后的文件可以部署到任何静态文件服务器：

1. 运行 `npm run build`
2. 将 `dist` 目录的内容上传到服务器
3. 确保服务器配置支持 SPA 路由

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 性能优化建议

1. **文件大小限制**：建议单个文件不超过 50MB
2. **浏览器内存**：处理大文件时可能需要较多内存
3. **网络环境**：首次加载需要下载所有依赖库

## 技术支持

如果遇到问题，请检查：

1. Node.js 版本是否符合要求
2. 浏览器控制台是否有错误信息
3. 网络连接是否正常
4. 文件格式是否受支持

## 更新日志

### v1.0.0
- 初始版本
- 支持图片、PDF、Word 文档水印
- 文件管理功能
- 响应式界面设计

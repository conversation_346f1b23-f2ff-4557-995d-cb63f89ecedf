<template>
  <div class="home">
    <div class="sidebar" :style="{ width: sidebarWidth + 'px' }">
      <FileManager
        @file-selected="handleFileSelected"
        @folder-selected="handleFolderSelected"
        ref="fileManagerRef"
      />
    </div>

    <div
      class="resize-handle"
      @mousedown="startResize"
    ></div>

    <div class="content">
      <WatermarkEditor
        :selected-file="selectedFile"
        :selected-folder="selectedFolder"
        @watermark-applied="handleWatermarkApplied"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FileManager from '@/components/FileManager.vue'
import WatermarkEditor from '@/components/WatermarkEditor.vue'

const selectedFile = ref(null)
const selectedFolder = ref(null)
const sidebarWidth = ref(280)
const isResizing = ref(false)
const fileManagerRef = ref(null)

// 处理文件选择
const handleFileSelected = (file) => {
  selectedFile.value = file
  selectedFolder.value = null
}

// 处理文件夹选择
const handleFolderSelected = (folder) => {
  selectedFolder.value = folder
  selectedFile.value = null
}

// 处理水印应用完成
const handleWatermarkApplied = () => {
  // 刷新文件列表
  if (fileManagerRef.value) {
    fileManagerRef.value.refreshFiles()
  }
}

// 开始调整侧边栏大小
const startResize = (e) => {
  isResizing.value = true
  const startX = e.clientX
  const startWidth = sidebarWidth.value

  const handleMouseMove = (e) => {
    if (!isResizing.value) return
    
    const newWidth = startWidth + (e.clientX - startX)
    if (newWidth >= 200 && newWidth <= 500) {
      sidebarWidth.value = newWidth
    }
  }

  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}
</script>

<style scoped>
.home {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  overflow: hidden;
  min-width: 200px;
  max-width: 500px;
}

.resize-handle {
  width: 4px;
  background: #e9ecef;
  cursor: col-resize;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background: #007bff;
}

.content {
  flex: 1;
  overflow: hidden;
}
</style>

-- 修复 Folders 表（不带外键）
USE WatermarkSys;

-- 删除外键约束检查
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有的 Folders 表（如果存在）
DROP TABLE IF EXISTS Folders;

-- 重新创建 Folders 表（不带外键约束）
CREATE TABLE Folders (
    Id varchar(36) NOT NULL,
    Name varchar(255) NOT NULL,
    ParentId varchar(36) DEFAULT NULL,
    Path varchar(1000) NOT NULL,
    Level int NOT NULL DEFAULT 0,
    IsSystem tinyint(1) NOT NULL DEFAULT 0,
    Description varchar(500) DEFAULT NULL,
    CreatedById varchar(36) NOT NULL,
    CreatedAt datetime NOT NULL,
    UpdatedAt datetime NOT NULL,
    PRIMARY KEY (Id),
    KEY IX_Folders_ParentId (ParentId),
    KEY IX_Folders_CreatedById (CreatedById),
    UNIQUE KEY IX_Folders_ParentId_Name (ParentId, Name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入根文件夹
INSERT INTO Folders (
    Id, 
    Name, 
    ParentId, 
    Path, 
    Level, 
    IsSystem, 
    Description, 
    CreatedById, 
    CreatedAt, 
    UpdatedAt
) VALUES (
    'root',
    '我的文件',
    NULL,
    '/',
    0,
    1,
    '系统根文件夹',
    'admin-user-id',
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00'
);

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证结果
SELECT 'Folders table created successfully' as Result;
SELECT Id, Name, ParentId, Path, Level, IsSystem, CreatedById FROM Folders;

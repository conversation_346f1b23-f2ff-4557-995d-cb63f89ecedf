using System.Security.Cryptography;
using System.Text;

namespace WatermarkCore.Services
{
    /// <summary>
    /// 文件存储服务实现
    /// </summary>
    public class FileStorageService : IFileStorageService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<FileStorageService> _logger;
        private readonly string _basePath;
        private readonly long _maxFileSize;
        private readonly HashSet<string> _allowedExtensions;

        public FileStorageService(IConfiguration configuration, ILogger<FileStorageService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            _basePath = _configuration["FileStorage:BasePath"] ?? "wwwroot/uploads";
            _maxFileSize = _configuration.GetValue<long>("FileStorage:MaxFileSize", 104857600); // 100MB
            
            var allowedExtensions = _configuration.GetSection("FileStorage:AllowedExtensions").Get<string[]>() 
                ?? new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".pdf", ".doc", ".docx" };
            _allowedExtensions = new HashSet<string>(allowedExtensions, StringComparer.OrdinalIgnoreCase);

            // 确保上传目录存在
            EnsureDirectoryExists(_basePath);
        }

        public async Task<FileValidationResult> ValidateFileAsync(IFormFile file)
        {
            var result = new FileValidationResult { IsValid = true };

            try
            {
                // 检查文件是否为空
                if (file == null || file.Length == 0)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "文件不能为空";
                    return result;
                }

                // 检查文件大小
                if (file.Length > _maxFileSize)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"文件大小不能超过 {_maxFileSize / 1024 / 1024} MB";
                    return result;
                }

                // 检查文件扩展名
                var extension = Path.GetExtension(file.FileName);
                if (string.IsNullOrEmpty(extension) || !_allowedExtensions.Contains(extension))
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"不支持的文件类型: {extension}";
                    return result;
                }

                // 检查文件名
                if (string.IsNullOrWhiteSpace(file.FileName) || file.FileName.Length > 255)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "文件名无效或过长";
                    return result;
                }

                // 检查文件内容类型
                var contentType = file.ContentType;
                if (string.IsNullOrEmpty(contentType))
                {
                    result.Warnings.Add("无法确定文件的MIME类型");
                }
                else
                {
                    var isValidContentType = extension.ToLower() switch
                    {
                        ".jpg" or ".jpeg" => contentType.StartsWith("image/jpeg"),
                        ".png" => contentType.StartsWith("image/png"),
                        ".gif" => contentType.StartsWith("image/gif"),
                        ".bmp" => contentType.StartsWith("image/bmp"),
                        ".webp" => contentType.StartsWith("image/webp"),
                        ".pdf" => contentType.StartsWith("application/pdf"),
                        ".doc" => contentType.Contains("msword"),
                        ".docx" => contentType.Contains("wordprocessingml"),
                        _ => true
                    };

                    if (!isValidContentType)
                    {
                        result.Warnings.Add($"文件扩展名与内容类型不匹配: {extension} vs {contentType}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件验证失败");
                result.IsValid = false;
                result.ErrorMessage = "文件验证过程中发生错误";
                return result;
            }
        }

        public async Task<FileSaveResult> SaveFileAsync(IFormFile file, string fileName)
        {
            var result = new FileSaveResult();

            try
            {
                // 生成唯一的文件路径
                var dateFolder = DateTime.UtcNow.ToString("yyyy/MM/dd");
                var uploadFolder = Path.Combine(_basePath, dateFolder);
                EnsureDirectoryExists(uploadFolder);

                var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
                var filePath = Path.Combine(uploadFolder, uniqueFileName);
                var relativePath = Path.Combine(dateFolder, uniqueFileName).Replace('\\', '/');

                // 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // 计算文件哈希
                var hash = await CalculateFileHashAsync(file);

                result.Success = true;
                result.FilePath = relativePath;
                result.FileHash = hash;

                _logger.LogInformation("文件保存成功: {FilePath}", relativePath);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件保存失败: {FileName}", fileName);
                result.Success = false;
                result.ErrorMessage = "文件保存失败";
                return result;
            }
        }

        public async Task<Stream?> GetFileStreamAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_basePath, filePath);
                if (!File.Exists(fullPath))
                {
                    _logger.LogWarning("文件不存在: {FilePath}", fullPath);
                    return null;
                }

                return new FileStream(fullPath, FileMode.Open, FileAccess.Read);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件流失败: {FilePath}", filePath);
                return null;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_basePath, filePath);
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    _logger.LogInformation("文件删除成功: {FilePath}", fullPath);
                    return true;
                }
                else
                {
                    _logger.LogWarning("要删除的文件不存在: {FilePath}", fullPath);
                    return true; // 文件不存在也算删除成功
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件删除失败: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_basePath, filePath);
                return File.Exists(fullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查文件存在性失败: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<long> GetFileSizeAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_basePath, filePath);
                if (File.Exists(fullPath))
                {
                    var fileInfo = new FileInfo(fullPath);
                    return fileInfo.Length;
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件大小失败: {FilePath}", filePath);
                return 0;
            }
        }

        public async Task<string> CalculateFileHashAsync(IFormFile file)
        {
            try
            {
                using var stream = file.OpenReadStream();
                using var sha256 = SHA256.Create();
                var hashBytes = await sha256.ComputeHashAsync(stream);
                return Convert.ToHexString(hashBytes).ToLower();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算文件哈希失败");
                return string.Empty;
            }
        }

        private void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
                _logger.LogInformation("创建目录: {Path}", path);
            }
        }
    }
}

-- 重新创建 Files 表的 SQL 脚本
USE WatermarkSys;

-- 创建 Files 表
CREATE TABLE IF NOT EXISTS `Files` (
    `Id` varchar(36) NOT NULL,
    `Name` varchar(255) NOT NULL,
    `OriginalName` varchar(255) NOT NULL,
    `FileType` varchar(50) NOT NULL,
    `Size` bigint NOT NULL,
    `Hash` varchar(64) NOT NULL,
    `Path` varchar(1000) NOT NULL,
    `WatermarkedPath` varchar(1000) DEFAULT NULL,
    `FolderId` varchar(36) NOT NULL,
    `UploadedById` varchar(36) NOT NULL,
    `CreatedAt` datetime NOT NULL,
    `UpdatedAt` datetime NOT NULL,
    `Description` varchar(500) DEFAULT NULL,
    `IsWatermarked` tinyint(1) NOT NULL DEFAULT 0,
    `WatermarkedAt` datetime DEFAULT NULL,
    `Status` varchar(20) NOT NULL DEFAULT 'Normal',
    `Tags` varchar(1000) DEFAULT NULL,
    `Metadata` text DEFAULT NULL,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `IX_Files_FolderId_Name` (`FolderId`, `Name`),
    KEY `IX_Files_FolderId` (`FolderId`),
    KEY `IX_Files_FileType` (`FileType`),
    KEY `IX_Files_Hash` (`Hash`),
    KEY `IX_Files_CreatedAt` (`CreatedAt`),
    KEY `IX_Files_UploadedById` (`UploadedById`),
    CONSTRAINT `FK_Files_Folders_FolderId` FOREIGN KEY (`FolderId`) REFERENCES `Folders` (`Id`) ON DELETE CASCADE,
    CONSTRAINT `FK_Files_Users_UploadedById` FOREIGN KEY (`UploadedById`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 检查表是否创建成功
SELECT 'Files table created successfully' as Result;

-- 显示所有表
SHOW TABLES;

-- 显示 Files 表结构
DESCRIBE Files;

namespace WatermarkCore.Middleware
{
    /// <summary>
    /// 文件上传中间件
    /// </summary>
    public class FileUploadMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<FileUploadMiddleware> _logger;
        private readonly long _maxRequestBodySize;

        public FileUploadMiddleware(RequestDelegate next, ILogger<FileUploadMiddleware> logger, IConfiguration configuration)
        {
            _next = next;
            _logger = logger;
            _maxRequestBodySize = configuration.GetValue<long>("FileStorage:MaxFileSize", 104857600); // 100MB
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 检查是否为文件上传请求
            if (IsFileUploadRequest(context))
            {
                // 设置请求体大小限制
                context.Features.Get<Microsoft.AspNetCore.Http.Features.IHttpMaxRequestBodySizeFeature>()!
                    .MaxRequestBodySize = _maxRequestBodySize;

                // 记录上传开始
                _logger.LogInformation("开始文件上传: {Path}", context.Request.Path);

                var startTime = DateTime.UtcNow;

                try
                {
                    await _next(context);

                    var duration = DateTime.UtcNow - startTime;
                    _logger.LogInformation("文件上传完成: {Path}, 耗时: {Duration}ms", 
                        context.Request.Path, duration.TotalMilliseconds);
                }
                catch (Exception ex)
                {
                    var duration = DateTime.UtcNow - startTime;
                    _logger.LogError(ex, "文件上传失败: {Path}, 耗时: {Duration}ms", 
                        context.Request.Path, duration.TotalMilliseconds);
                    throw;
                }
            }
            else
            {
                await _next(context);
            }
        }

        private static bool IsFileUploadRequest(HttpContext context)
        {
            return context.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase) &&
                   context.Request.ContentType != null &&
                   context.Request.ContentType.Contains("multipart/form-data", StringComparison.OrdinalIgnoreCase);
        }
    }
}

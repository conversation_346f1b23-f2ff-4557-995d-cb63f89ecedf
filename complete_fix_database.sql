-- 完整修复数据库结构
USE WatermarkSys;

-- 1. 检查当前表结构
SELECT 'Current tables:' as Info;
SHOW TABLES;

-- 2. 检查 Folders 表结构
SELECT 'Folders table structure:' as Info;
DESCRIBE Folders;

-- 3. 删除可能存在的外键约束（如果存在）
SET FOREIGN_KEY_CHECKS = 0;

-- 4. 删除现有的 Folders 表并重新创建
DROP TABLE IF EXISTS Folders;

-- 5. 重新创建 Folders 表，包含所有必要字段
CREATE TABLE Folders (
    Id varchar(36) NOT NULL,
    Name varchar(255) NOT NULL,
    ParentId varchar(36) DEFAULT NULL,
    Path varchar(1000) NOT NULL,
    Level int NOT NULL DEFAULT 0,
    IsSystem tinyint(1) NOT NULL DEFAULT 0,
    Description varchar(500) DEFAULT NULL,
    CreatedById varchar(36) NOT NULL,
    CreatedAt datetime NOT NULL,
    UpdatedAt datetime NOT NULL,
    PRIMARY KEY (Id),
    KEY IX_Folders_ParentId (ParentId),
    KEY IX_Folders_CreatedById (CreatedById),
    UNIQUE KEY IX_Folders_ParentId_Name (ParentId, Name),
    CONSTRAINT FK_Folders_Folders_ParentId FOREIGN KEY (ParentId) REFERENCES Folders (Id) ON DELETE RESTRICT,
    CONSTRAINT FK_Folders_Users_CreatedById FOREIGN KEY (CreatedById) REFERENCES Users (Id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 6. 插入根文件夹
INSERT INTO Folders (
    Id, 
    Name, 
    ParentId, 
    Path, 
    Level, 
    IsSystem, 
    Description, 
    CreatedById, 
    CreatedAt, 
    UpdatedAt
) VALUES (
    'root',
    '我的文件',
    NULL,
    '/',
    0,
    1,
    '系统根文件夹',
    'admin-user-id',
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00'
);

-- 7. 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 8. 验证结果
SELECT 'Final Folders table structure:' as Info;
DESCRIBE Folders;

SELECT 'Folders data:' as Info;
SELECT Id, Name, ParentId, Path, Level, IsSystem, CreatedById FROM Folders;

-- 9. 检查所有表的外键关系
SELECT 'Foreign key constraints:' as Info;
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    REFERENCED_TABLE_SCHEMA = 'WatermarkSys' 
    AND TABLE_NAME IN ('Folders', 'Files', 'Users')
ORDER BY TABLE_NAME, COLUMN_NAME;

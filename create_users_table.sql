-- 创建 Users 表的 SQL 脚本
USE WatermarkSys;

-- 创建 Users 表
CREATE TABLE IF NOT EXISTS `Users` (
    `Id` varchar(36) NOT NULL,
    `Username` varchar(50) NOT NULL,
    `Email` varchar(100) NOT NULL,
    `PasswordHash` varchar(255) NOT NULL,
    `DisplayName` varchar(100) DEFAULT NULL,
    `Role` varchar(20) NOT NULL DEFAULT 'User',
    `IsActive` tinyint(1) NOT NULL DEFAULT 1,
    `CanDeleteFiles` tinyint(1) NOT NULL DEFAULT 0,
    `CanDownloadOriginalFiles` tinyint(1) NOT NULL DEFAULT 0,
    `CreatedAt` datetime NOT NULL,
    `UpdatedAt` datetime NOT NULL,
    `LastLoginAt` datetime DEFAULT NULL,
    `CreatedById` varchar(36) DEFAULT NULL,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `IX_Users_Username` (`Username`),
    UNIQUE KEY `IX_Users_Email` (`Email`),
    KEY `IX_Users_Role` (`Role`),
    KEY `IX_Users_CreatedAt` (`CreatedAt`),
    KEY `IX_Users_CreatedById` (`CreatedById`),
    CONSTRAINT `FK_Users_Users_CreatedById` FOREIGN KEY (`CreatedById`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 插入默认管理员用户
INSERT INTO `Users` (`Id`, `Username`, `Email`, `PasswordHash`, `DisplayName`, `Role`, `IsActive`, `CanDeleteFiles`, `CanDownloadOriginalFiles`, `CreatedAt`, `UpdatedAt`, `CreatedById`) 
VALUES (
    'admin-user-id',
    'admin',
    '<EMAIL>',
    '$2a$11$8K1p/a0dclxViuPizdDtaOCjmg8rFcXvdlJGjMxcCOlWMpjAr6vTa', -- 这是 "admin123" 的 BCrypt 哈希
    '系统管理员',
    'Admin',
    1,
    1,
    1,
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00',
    NULL
);

-- 检查是否需要更新现有的 files 和 folders 表以添加外键约束
-- 如果 files 表中有 UploadedById 字段但没有外键约束，添加它
ALTER TABLE `files` 
ADD CONSTRAINT `FK_Files_Users_UploadedById` 
FOREIGN KEY (`UploadedById`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT;

-- 如果 folders 表中有 CreatedById 字段但没有外键约束，添加它
ALTER TABLE `folders` 
ADD CONSTRAINT `FK_Folders_Users_CreatedById` 
FOREIGN KEY (`CreatedById`) REFERENCES `Users` (`Id`) ON DELETE RESTRICT;

-- 显示创建结果
SELECT 'Users table created successfully' as Result;
SHOW TABLES;

-- 最终修复管理员用户
USE WatermarkSys;

-- 查看当前用户
SELECT 'Current users before fix:' as Info;
SELECT Username, Email, Role, IsActive, SUBSTRING(PasswordHash, 1, 30) as PasswordHashPreview FROM Users;

-- 删除现有的admin用户
DELETE FROM Users WHERE Username = 'admin';

-- 插入管理员用户，使用已验证的BCrypt哈希
-- 这个哈希对应密码 "admin123"
INSERT INTO Users (
    Id, 
    Username, 
    Email, 
    PasswordHash, 
    DisplayName, 
    Role, 
    IsActive, 
    CanDeleteFiles, 
    CanDownloadOriginalFiles, 
    CreatedAt, 
    UpdatedAt,
    CreatedById
) VALUES (
    'admin-user-id',
    'admin',
    '<EMAIL>',
    '$2a$11$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    '系统管理员',
    'Admin',
    1,
    1,
    1,
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00',
    NULL
);

-- 验证插入结果
SELECT 'Admin user after fix:' as Info;
SELECT Id, Username, Email, DisplayName, Role, IsActive, 
       SUBSTRING(PasswordHash, 1, 30) as PasswordHashPreview 
FROM Users WHERE Username = 'admin';

-- 显示所有用户
SELECT 'All users after fix:' as Info;
SELECT Username, Email, Role, IsActive FROM Users;

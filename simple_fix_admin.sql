-- 简单修复管理员用户
USE WatermarkSys;

-- 查看当前用户
SELECT 'Current users:' as Info;
SELECT Username, Email, Role, IsActive FROM Users;

-- 删除所有现有用户
DELETE FROM Users;

-- 插入一个简单的管理员用户，先用一个临时密码
-- 我们稍后会通过应用程序API来设置正确的密码
INSERT INTO Users (
    Id, 
    Username, 
    Email, 
    PasswordHash, 
    DisplayName, 
    Role, 
    IsActive, 
    CanDeleteFiles, 
    CanDownloadOriginalFiles, 
    CreatedAt, 
    UpdatedAt
) VALUES (
    'admin-user-id',
    'admin',
    '<EMAIL>',
    'temp_password_hash',
    '系统管理员',
    'Admin',
    1,
    1,
    1,
    NOW(),
    NOW()
);

-- 验证插入
SELECT 'After insert:' as Info;
SELECT Username, Email, Role, IsActive FROM Users;

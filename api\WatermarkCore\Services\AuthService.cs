using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using WatermarkCore.Data;
using WatermarkCore.Models;
using WatermarkCore.Models.DTOs;

namespace WatermarkCore.Services
{
    /// <summary>
    /// 认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly WatermarkDbContext _context;
        private readonly JwtSettings _jwtSettings;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            WatermarkDbContext context,
            IOptions<JwtSettings> jwtSettings,
            ILogger<AuthService> logger)
        {
            _context = context;
            _jwtSettings = jwtSettings.Value;
            _logger = logger;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        public async Task<LoginResponseDto?> LoginAsync(LoginRequestDto loginRequest)
        {
            try
            {
                // 查找用户
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == loginRequest.Username && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("登录失败：用户名 {Username} 不存在或已禁用", loginRequest.Username);
                    return null;
                }

                // 验证密码
                if (!VerifyPassword(loginRequest.Password, user.PasswordHash))
                {
                    _logger.LogWarning("登录失败：用户 {Username} 密码错误", loginRequest.Username);
                    return null;
                }

                // 更新最后登录时间
                await UpdateLastLoginAsync(user.Id);

                // 生成JWT令牌
                var token = GenerateJwtToken(user);
                var expiresAt = DateTimeOffset.UtcNow.AddMinutes(_jwtSettings.ExpirationInMinutes).ToUnixTimeSeconds();

                _logger.LogInformation("用户 {Username} 登录成功", loginRequest.Username);

                return new LoginResponseDto
                {
                    AccessToken = token,
                    TokenType = "Bearer",
                    ExpiresAt = expiresAt,
                    User = MapToUserDto(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生错误");
                return null;
            }
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        public async Task<UserDto?> RegisterAsync(RegisterRequestDto registerRequest, string? createdById = null)
        {
            try
            {
                // 检查用户名是否已存在
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == registerRequest.Username || u.Email == registerRequest.Email);

                if (existingUser != null)
                {
                    _logger.LogWarning("注册失败：用户名 {Username} 或邮箱 {Email} 已存在", 
                        registerRequest.Username, registerRequest.Email);
                    return null;
                }

                // 创建新用户
                var user = new User
                {
                    Id = Guid.NewGuid().ToString(),
                    Username = registerRequest.Username,
                    Email = registerRequest.Email,
                    PasswordHash = HashPassword(registerRequest.Password),
                    DisplayName = registerRequest.DisplayName ?? registerRequest.Username,
                    Role = string.IsNullOrEmpty(registerRequest.Role) ? UserRoles.User : registerRequest.Role,
                    IsActive = true,
                    CanDeleteFiles = false,
                    CanDownloadOriginalFiles = false,
                    CreatedById = createdById,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 注册成功", registerRequest.Username);

                return MapToUserDto(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生错误");
                return null;
            }
        }

        /// <summary>
        /// 生成JWT令牌
        /// </summary>
        public string GenerateJwtToken(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_jwtSettings.SecretKey);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Name, user.Username),
                new(ClaimTypes.Email, user.Email),
                new(ClaimTypes.Role, user.Role),
                new("DisplayName", user.DisplayName ?? user.Username),
                new("CanDeleteFiles", user.CanDeleteFiles.ToString()),
                new("CanDownloadOriginalFiles", user.CanDownloadOriginalFiles.ToString())
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpirationInMinutes),
                Issuer = _jwtSettings.Issuer,
                Audience = _jwtSettings.Audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        public bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }

        /// <summary>
        /// 哈希密码
        /// </summary>
        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        public async Task<UserDto?> GetUserByIdAsync(string userId)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive);

                return user != null ? MapToUserDto(user) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                return user != null ? MapToUserDto(user) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 更新用户最后登录时间
        /// </summary>
        public async Task UpdateLastLoginAsync(string userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user != null)
                {
                    user.LastLoginAt = DateTime.UtcNow;
                    user.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新最后登录时间时发生错误");
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        public async Task<bool> ChangePasswordAsync(string userId, ChangePasswordRequestDto changePasswordRequest)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                // 验证当前密码
                if (!VerifyPassword(changePasswordRequest.CurrentPassword, user.PasswordHash))
                {
                    return false;
                }

                // 更新密码
                user.PasswordHash = HashPassword(changePasswordRequest.NewPassword);
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {UserId} 修改密码成功", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 更新用户权限
        /// </summary>
        public async Task<bool> UpdateUserPermissionsAsync(string userId, UpdateUserPermissionsRequestDto permissionsRequest, string operatorId)
        {
            try
            {
                // 验证操作者是否为管理员
                var operatorUser = await _context.Users.FindAsync(operatorId);
                if (operatorUser?.Role != UserRoles.Admin)
                {
                    return false;
                }

                var user = await _context.Users.FindAsync(userId);
                if (user == null || user.Role == UserRoles.Admin)
                {
                    return false; // 不能修改管理员权限
                }

                user.CanDeleteFiles = permissionsRequest.CanDeleteFiles;
                user.CanDownloadOriginalFiles = permissionsRequest.CanDownloadOriginalFiles;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("管理员 {OperatorId} 更新用户 {UserId} 权限成功", operatorId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户权限时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        public async Task<List<UserDto>> GetUsersAsync(string operatorId)
        {
            try
            {
                // 验证操作者是否为管理员
                var operatorUser = await _context.Users.FindAsync(operatorId);
                if (operatorUser?.Role != UserRoles.Admin)
                {
                    return new List<UserDto>();
                }

                var users = await _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.CreatedAt)
                    .ToListAsync();

                return users.Select(MapToUserDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表时发生错误");
                return new List<UserDto>();
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        public async Task<bool> DeleteUserAsync(string userId, string operatorId)
        {
            try
            {
                // 验证操作者是否为管理员
                var operatorUser = await _context.Users.FindAsync(operatorId);
                if (operatorUser?.Role != UserRoles.Admin)
                {
                    return false;
                }

                var user = await _context.Users.FindAsync(userId);
                if (user == null || user.Role == UserRoles.Admin)
                {
                    return false; // 不能删除管理员
                }

                // 软删除：设置为非激活状态
                user.IsActive = false;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("管理员 {OperatorId} 删除用户 {UserId} 成功", operatorId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 将User实体映射为UserDto
        /// </summary>
        private static UserDto MapToUserDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                DisplayName = user.DisplayName,
                Role = user.Role,
                IsActive = user.IsActive,
                CanDeleteFiles = user.CanDeleteFiles,
                CanDownloadOriginalFiles = user.CanDownloadOriginalFiles,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt,
                CreatedById = user.CreatedById
            };
        }
    }
}

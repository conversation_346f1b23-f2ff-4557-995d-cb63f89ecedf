-- 创建简单的 Folders 表
USE WatermarkSys;

-- 删除外键约束检查
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有的 Folders 表（如果存在）
DROP TABLE IF EXISTS Folders;

-- 创建最简单的 Folders 表
CREATE TABLE Folders (
    Id varchar(36) NOT NULL PRIMARY KEY,
    Name varchar(255) NOT NULL,
    ParentId varchar(36) DEFAULT NULL,
    Path varchar(1000) NOT NULL,
    Level int NOT NULL DEFAULT 0,
    IsSystem tinyint(1) NOT NULL DEFAULT 0,
    Description varchar(500) DEFAULT NULL,
    CreatedById varchar(36) NOT NULL,
    CreatedAt datetime NOT NULL,
    UpdatedAt datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入根文件夹
INSERT INTO Folders (
    Id, 
    Name, 
    ParentId, 
    Path, 
    Level, 
    IsSystem, 
    Description, 
    CreatedById, 
    CreatedAt, 
    UpdatedAt
) VALUES (
    'root',
    '我的文件',
    NULL,
    '/',
    0,
    1,
    '系统根文件夹',
    'admin-user-id',
    '2024-01-01 00:00:00',
    '2024-01-01 00:00:00'
);

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证结果
SELECT 'Folders table created successfully' as Result;
SELECT * FROM Folders;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using WatermarkCore.Data;
using WatermarkCore.Models;
using WatermarkCore.Models.DTOs;
using WatermarkCore.Services;
using WatermarkCore.Middleware;

namespace WatermarkCore.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // 需要登录才能访问
    public class FilesController : ControllerBase
    {
        private readonly WatermarkDbContext _context;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<FilesController> _logger;

        public FilesController(
            WatermarkDbContext context,
            IFileStorageService fileStorageService,
            ILogger<FilesController> logger)
        {
            _context = context;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<FileItemDto>>> GetFiles([FromQuery] string? folderId = null)
        {
            try
            {
                var query = _context.Files.AsQueryable();

                if (!string.IsNullOrEmpty(folderId))
                {
                    query = query.Where(f => f.FolderId == folderId);
                }

                var files = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .ToListAsync();

                var fileDtos = files.Select(MapToDto).ToList();
                return Ok(fileDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件列表失败");
                return StatusCode(500, "获取文件列表失败");
            }
        }

        /// <summary>
        /// 根据ID获取文件
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<FileItemDto>> GetFile(string id)
        {
            try
            {
                var file = await _context.Files
                    .Include(f => f.Folder)
                    .FirstOrDefaultAsync(f => f.Id == id);

                if (file == null)
                {
                    return NotFound("文件不存在");
                }

                return Ok(MapToDto(file));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件失败: {FileId}", id);
                return StatusCode(500, "获取文件失败");
            }
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        [HttpPost("upload")]
        public async Task<ActionResult<FileItemDto>> UploadFile([FromForm] UploadFileDto uploadDto)
        {
            try
            {
                // 获取当前用户ID
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                if (uploadDto.File == null || uploadDto.File.Length == 0)
                {
                    return BadRequest("请选择要上传的文件");
                }

                // 验证文件夹是否存在
                var folderExists = await _context.Folders.AnyAsync(f => f.Id == uploadDto.FolderId);
                if (!folderExists)
                {
                    return BadRequest("目标文件夹不存在");
                }

                // 验证文件类型和大小
                var validationResult = await _fileStorageService.ValidateFileAsync(uploadDto.File);
                if (!validationResult.IsValid)
                {
                    return BadRequest(validationResult.ErrorMessage);
                }

                // 检查文件名是否重复
                var originalName = Path.GetFileNameWithoutExtension(uploadDto.File.FileName);
                var extension = Path.GetExtension(uploadDto.File.FileName);
                var fileName = uploadDto.File.FileName;
                var counter = 1;

                while (await _context.Files.AnyAsync(f => f.FolderId == uploadDto.FolderId && f.Name == fileName))
                {
                    fileName = $"{originalName}({counter}){extension}";
                    counter++;
                }

                // 保存文件到存储
                var storageResult = await _fileStorageService.SaveFileAsync(uploadDto.File, fileName);
                if (!storageResult.Success)
                {
                    return StatusCode(500, storageResult.ErrorMessage);
                }

                // 创建文件记录
                var fileItem = new FileItem
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = fileName,
                    OriginalName = uploadDto.File.FileName,
                    FileType = GetFileType(extension),
                    MimeType = uploadDto.File.ContentType,
                    Size = uploadDto.File.Length,
                    Extension = extension,
                    StoragePath = storageResult.FilePath!,
                    Hash = storageResult.FileHash,
                    FolderId = uploadDto.FolderId,
                    UploadedById = userId,
                    Description = uploadDto.Description,
                    Tags = uploadDto.Tags,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Files.Add(fileItem);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetFile), new { id = fileItem.Id }, MapToDto(fileItem));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件上传失败");
                return StatusCode(500, "文件上传失败");
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        [HttpGet("{id}/download")]
        [Permission(Permissions.CanDownloadOriginalFiles)]
        public async Task<ActionResult> DownloadFile(string id)
        {
            try
            {
                var file = await _context.Files.FindAsync(id);
                if (file == null)
                {
                    return NotFound("文件不存在");
                }

                var fileStream = await _fileStorageService.GetFileStreamAsync(file.StoragePath);
                if (fileStream == null)
                {
                    return NotFound("文件内容不存在");
                }

                return File(fileStream, file.MimeType, file.OriginalName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件下载失败: {FileId}", id);
                return StatusCode(500, "文件下载失败");
            }
        }

        /// <summary>
        /// 更新文件信息
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<FileItemDto>> UpdateFile(string id, [FromBody] UpdateFileDto updateDto)
        {
            try
            {
                var file = await _context.Files.FindAsync(id);
                if (file == null)
                {
                    return NotFound("文件不存在");
                }

                // 检查文件名是否重复
                if (file.Name != updateDto.Name)
                {
                    var duplicateExists = await _context.Files
                        .AnyAsync(f => f.FolderId == file.FolderId && f.Name == updateDto.Name && f.Id != id);
                    if (duplicateExists)
                    {
                        return BadRequest("同一文件夹下已存在同名文件");
                    }
                }

                file.Name = updateDto.Name;
                file.Description = updateDto.Description;
                file.Tags = updateDto.Tags;
                file.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(MapToDto(file));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新文件失败: {FileId}", id);
                return StatusCode(500, "更新文件失败");
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        [HttpDelete("{id}")]
        [Permission(Permissions.CanDeleteFiles)]
        public async Task<ActionResult> DeleteFile(string id)
        {
            try
            {
                var file = await _context.Files.FindAsync(id);
                if (file == null)
                {
                    return NotFound("文件不存在");
                }

                // 删除物理文件
                await _fileStorageService.DeleteFileAsync(file.StoragePath);

                // 删除数据库记录
                _context.Files.Remove(file);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除文件失败: {FileId}", id);
                return StatusCode(500, "删除文件失败");
            }
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        [HttpPut("{id}/move")]
        public async Task<ActionResult<FileItemDto>> MoveFile(string id, [FromBody] MoveFileDto moveDto)
        {
            try
            {
                var file = await _context.Files.FindAsync(id);
                if (file == null)
                {
                    return NotFound("文件不存在");
                }

                // 验证目标文件夹
                var targetFolderExists = await _context.Folders.AnyAsync(f => f.Id == moveDto.TargetFolderId);
                if (!targetFolderExists)
                {
                    return BadRequest("目标文件夹不存在");
                }

                // 检查目标文件夹是否有同名文件
                var duplicateExists = await _context.Files
                    .AnyAsync(f => f.FolderId == moveDto.TargetFolderId && f.Name == file.Name && f.Id != id);
                if (duplicateExists)
                {
                    return BadRequest("目标文件夹已存在同名文件");
                }

                file.FolderId = moveDto.TargetFolderId;
                file.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(MapToDto(file));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动文件失败: {FileId}", id);
                return StatusCode(500, "移动文件失败");
            }
        }

        /// <summary>
        /// 搜索文件
        /// </summary>
        [HttpPost("search")]
        public async Task<ActionResult<SearchFileResultDto>> SearchFiles([FromBody] SearchFileDto searchDto)
        {
            try
            {
                var query = _context.Files.AsQueryable();

                // 关键词搜索
                if (!string.IsNullOrEmpty(searchDto.Keyword))
                {
                    query = query.Where(f => f.Name.Contains(searchDto.Keyword) || 
                                           f.Description!.Contains(searchDto.Keyword));
                }

                // 文件类型过滤
                if (!string.IsNullOrEmpty(searchDto.FileType))
                {
                    query = query.Where(f => f.FileType == searchDto.FileType);
                }

                // 文件夹过滤
                if (!string.IsNullOrEmpty(searchDto.FolderId))
                {
                    query = query.Where(f => f.FolderId == searchDto.FolderId);
                }

                // 日期范围过滤
                if (searchDto.StartDate.HasValue)
                {
                    query = query.Where(f => f.CreatedAt >= searchDto.StartDate.Value);
                }
                if (searchDto.EndDate.HasValue)
                {
                    query = query.Where(f => f.CreatedAt <= searchDto.EndDate.Value);
                }

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize);

                var files = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .ToListAsync();

                var result = new SearchFileResultDto
                {
                    Files = files.Select(MapToDto).ToList(),
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = totalPages
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索文件失败");
                return StatusCode(500, "搜索文件失败");
            }
        }

        /// <summary>
        /// 批量操作文件
        /// </summary>
        [HttpPost("batch")]
        public async Task<ActionResult> BatchOperation([FromBody] BatchOperationDto batchDto)
        {
            try
            {
                var files = await _context.Files
                    .Where(f => batchDto.FileIds.Contains(f.Id))
                    .ToListAsync();

                if (!files.Any())
                {
                    return BadRequest("未找到指定的文件");
                }

                switch (batchDto.Operation.ToLower())
                {
                    case "delete":
                        foreach (var file in files)
                        {
                            await _fileStorageService.DeleteFileAsync(file.StoragePath);
                        }
                        _context.Files.RemoveRange(files);
                        break;

                    case "move":
                        if (string.IsNullOrEmpty(batchDto.TargetFolderId))
                        {
                            return BadRequest("移动操作需要指定目标文件夹");
                        }

                        var targetFolderExists = await _context.Folders.AnyAsync(f => f.Id == batchDto.TargetFolderId);
                        if (!targetFolderExists)
                        {
                            return BadRequest("目标文件夹不存在");
                        }

                        foreach (var file in files)
                        {
                            file.FolderId = batchDto.TargetFolderId;
                            file.UpdatedAt = DateTime.UtcNow;
                        }
                        break;

                    case "tag":
                        foreach (var file in files)
                        {
                            file.Tags = batchDto.Tags;
                            file.UpdatedAt = DateTime.UtcNow;
                        }
                        break;

                    default:
                        return BadRequest("不支持的操作类型");
                }

                await _context.SaveChangesAsync();
                return Ok($"成功处理 {files.Count} 个文件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量操作失败");
                return StatusCode(500, "批量操作失败");
            }
        }

        #region 私有方法

        private static FileItemDto MapToDto(FileItem file)
        {
            return new FileItemDto
            {
                Id = file.Id,
                Name = file.Name,
                OriginalName = file.OriginalName,
                FileType = file.FileType,
                MimeType = file.MimeType,
                Size = file.Size,
                Extension = file.Extension,
                FolderId = file.FolderId,
                Description = file.Description,
                IsWatermarked = file.IsWatermarked,
                WatermarkedAt = file.WatermarkedAt,
                Status = file.Status,
                Tags = file.Tags,
                Metadata = file.Metadata,
                CreatedAt = file.CreatedAt,
                UpdatedAt = file.UpdatedAt
            };
        }

        private static string GetFileType(string extension)
        {
            return extension.ToLower() switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => "image",
                ".pdf" => "pdf",
                ".doc" or ".docx" => "word",
                _ => "unknown"
            };
        }

        #endregion
    }
}

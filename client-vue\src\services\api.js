import axios from 'axios'

// API 基础配置
const API_BASE_URL = 'http://localhost:5072/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data)

    // 添加认证令牌
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url, response.data)
    return response
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.response?.data || error.message)

    // 统一错误处理
    if (error.response?.status === 401) {
      // 未授权，清除本地令牌并跳转到登录页
      localStorage.removeItem('accessToken')
      localStorage.removeItem('tokenExpiresAt')

      // 如果不是在登录页面，则跳转到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    } else if (error.response?.status === 403) {
      console.warn('Access forbidden:', error.config?.url)
    }

    // 提取错误信息
    let errorMessage = 'API请求失败'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.response?.data?.details) {
      errorMessage = error.response.data.details
    } else if (error.response?.data) {
      errorMessage = typeof error.response.data === 'string' ? error.response.data : JSON.stringify(error.response.data)
    } else if (error.message) {
      errorMessage = error.message
    }

    const customError = new Error(errorMessage)
    customError.status = error.response?.status
    customError.response = error.response

    return Promise.reject(customError)
  }
)

// 认证API
export const authApi = {
  // 用户登录
  async login(credentials) {
    try {
      const response = await apiClient.post('/auth/login', credentials)
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 用户注册
  async register(userData) {
    try {
      const response = await apiClient.post('/auth/register', userData)
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    try {
      const response = await apiClient.get('/auth/me')
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 修改密码
  async changePassword(passwordData) {
    try {
      const response = await apiClient.post('/auth/change-password', passwordData)
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 用户登出
  async logout() {
    try {
      const response = await apiClient.post('/auth/logout')
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 获取用户列表（仅管理员）
  async getUsers() {
    try {
      const response = await apiClient.get('/auth/users')
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 更新用户权限（仅管理员）
  async updateUserPermissions(userId, permissions) {
    try {
      const response = await apiClient.put(`/auth/users/${userId}/permissions`, permissions)
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  },

  // 删除用户（仅管理员）
  async deleteUser(userId) {
    try {
      const response = await apiClient.delete(`/auth/users/${userId}`)
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

// 文件夹API
export const foldersApi = {
  // 获取所有文件夹
  async getFolders() {
    const response = await apiClient.get('/folders')
    return response.data
  },

  // 获取文件夹树
  async getFolderTree() {
    const response = await apiClient.get('/folders/tree')
    return response.data
  },

  // 根据ID获取文件夹
  async getFolder(id) {
    const response = await apiClient.get(`/folders/${id}`)
    return response.data
  },

  // 创建文件夹
  async createFolder(data) {
    const response = await apiClient.post('/folders', data)
    return response.data
  },

  // 更新文件夹
  async updateFolder(id, data) {
    const response = await apiClient.put(`/folders/${id}`, data)
    return response.data
  },

  // 删除文件夹
  async deleteFolder(id, recursive = false) {
    const params = recursive ? { recursive: true } : {}
    await apiClient.delete(`/folders/${id}`, { params })
    return true
  },

  // 重命名文件夹
  async renameFolder(id, newName) {
    const response = await apiClient.put(`/folders/${id}/rename`, { newName })
    return response.data
  },

  // 移动文件夹
  async moveFolder(id, targetParentId) {
    const response = await apiClient.put(`/folders/${id}/move`, { targetParentId })
    return response.data
  },
}

// 文件API
export const filesApi = {
  // 获取文件列表
  async getFiles(folderId = null) {
    const params = folderId ? { folderId } : {}
    const response = await apiClient.get('/files', { params })
    return response.data
  },

  // 根据ID获取文件
  async getFile(id) {
    const response = await apiClient.get(`/files/${id}`)
    return response.data
  },

  // 上传文件
  async uploadFile(file, folderId, description = '', tags = '') {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('folderId', folderId)
    if (description) formData.append('description', description)
    if (tags) formData.append('tags', tags)

    const response = await apiClient.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  // 下载文件
  async downloadFile(id) {
    const response = await apiClient.get(`/files/${id}/download`, {
      responseType: 'blob',
    })
    return response.data
  },

  // 更新文件
  async updateFile(id, data) {
    const response = await apiClient.put(`/files/${id}`, data)
    return response.data
  },

  // 删除文件
  async deleteFile(id) {
    await apiClient.delete(`/files/${id}`)
    return true
  },

  // 移动文件
  async moveFile(id, targetFolderId) {
    const response = await apiClient.put(`/files/${id}/move`, { targetFolderId })
    return response.data
  },

  // 搜索文件
  async searchFiles(searchParams) {
    const response = await apiClient.post('/files/search', searchParams)
    return response.data
  },

  // 批量操作文件
  async batchOperation(fileIds, operation, options = {}) {
    const response = await apiClient.post('/files/batch', {
      fileIds,
      operation,
      ...options,
    })
    return response.data
  },
}

// 导出默认API对象
export default {
  folders: foldersApi,
  files: filesApi,
}

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export const useFileStore = defineStore('file', () => {
  // 状态
  const files = ref([])
  const folders = ref([])
  const fileTree = ref([])
  const selectedFile = ref(null)
  const currentFolder = ref('root')
  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const currentFolderFiles = computed(() => {
    return files.value.filter(file => file.folderId === currentFolder.value)
  })

  const currentFolderSubfolders = computed(() => {
    return folders.value.filter(folder => folder.parentId === currentFolder.value)
  })



  // 初始化数据
  const initializeData = async () => {
    try {
      loading.value = true
      error.value = null

      // 获取文件夹树
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      // 获取所有文件夹（平铺结构，用于其他操作）
      const allFolders = await api.folders.getFolders()
      folders.value = allFolders || []

      // 获取当前文件夹的文件
      await loadFiles(currentFolder.value)
    } catch (err) {
      error.value = err.message
      ElMessage.error('加载数据失败: ' + err.message)
    } finally {
      loading.value = false
    }
  }

  // 加载文件列表
  const loadFiles = async (folderId = null) => {
    try {
      const fileList = await api.files.getFiles(folderId)
      files.value = fileList || []
    } catch (err) {
      error.value = err.message
      ElMessage.error('加载文件失败: ' + err.message)
    }
  }

  // 操作方法
  const addFile = async (fileData) => {
    try {
      loading.value = true
      const newFile = await api.files.uploadFile(
        fileData.file,
        fileData.folderId || currentFolder.value,
        fileData.description,
        fileData.tags
      )

      // 如果是当前文件夹，添加到本地列表
      if (newFile.folderId === currentFolder.value) {
        files.value.push(newFile)
      }

      // 重新加载文件树以反映更改
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      ElMessage.success('文件上传成功')
      return newFile
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件上传失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const addFolder = async (folderName, parentId = currentFolder.value) => {
    try {
      loading.value = true
      const newFolder = await api.folders.createFolder({
        name: folderName,
        parentId: parentId || null,
        description: ''
      })

      // 重新加载文件夹树和文件夹列表
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      const allFolders = await api.folders.getFolders()
      folders.value = allFolders || []

      ElMessage.success('文件夹创建成功')
      return newFolder
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件夹创建失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteFile = async (fileId) => {
    try {
      loading.value = true
      await api.files.deleteFile(fileId)

      // 从本地列表移除
      const index = files.value.findIndex(file => file.id === fileId)
      if (index > -1) {
        files.value.splice(index, 1)
        if (selectedFile.value && selectedFile.value.id === fileId) {
          selectedFile.value = null
        }
      }

      // 重新加载文件树以反映更改
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      ElMessage.success('文件删除成功')
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件删除失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteFolder = async (folderId, recursive = false) => {
    try {
      loading.value = true
      await api.folders.deleteFolder(folderId, recursive)

      // 重新加载文件夹树和文件夹列表
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      const allFolders = await api.folders.getFolders()
      folders.value = allFolders || []

      // 如果删除的是当前文件夹，切换到根文件夹
      if (currentFolder.value === folderId) {
        currentFolder.value = 'root'
        await loadFiles('root')
      } else {
        // 重新加载当前文件夹的文件
        await loadFiles(currentFolder.value)
      }

      ElMessage.success('文件夹删除成功')
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件夹删除失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const selectFile = (file) => {
    selectedFile.value = file
  }

  const setCurrentFolder = async (folderId) => {
    currentFolder.value = folderId
    await loadFiles(folderId)
  }

  const moveFile = async (fileId, targetFolderId) => {
    try {
      loading.value = true
      await api.files.moveFile(fileId, targetFolderId)

      // 从当前文件夹列表移除
      const index = files.value.findIndex(f => f.id === fileId)
      if (index > -1) {
        files.value.splice(index, 1)
      }

      // 重新加载文件树以反映更改
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      ElMessage.success('文件移动成功')
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件移动失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const renameFile = async (fileId, newName) => {
    try {
      loading.value = true
      const updatedFile = await api.files.updateFile(fileId, { name: newName })

      // 更新本地文件
      const file = files.value.find(f => f.id === fileId)
      if (file) {
        Object.assign(file, updatedFile)
      }

      // 重新加载文件树以反映更改
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      ElMessage.success('文件重命名成功')
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件重命名失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const renameFolder = async (folderId, newName) => {
    try {
      loading.value = true
      await api.folders.renameFolder(folderId, newName)

      // 重新加载文件夹树和文件夹列表
      const folderTreeData = await api.folders.getFolderTree()
      fileTree.value = folderTreeData || []

      const allFolders = await api.folders.getFolders()
      folders.value = allFolders || []

      ElMessage.success('文件夹重命名成功')
    } catch (err) {
      error.value = err.message
      ElMessage.error('文件夹重命名失败: ' + err.message)
      throw err
    } finally {
      loading.value = false
    }
  }

  const getFileById = (fileId) => {
    return files.value.find(file => file.id === fileId)
  }

  const getFolderById = (folderId) => {
    return folders.value.find(folder => folder.id === folderId)
  }

  const getFilesByType = (type) => {
    return files.value.filter(file => file.type === type)
  }

  const searchFiles = (keyword) => {
    const lowerKeyword = keyword.toLowerCase()
    return files.value.filter(file => 
      file.name.toLowerCase().includes(lowerKeyword)
    )
  }

  const getFileStats = () => {
    const stats = {
      total: files.value.length,
      images: 0,
      pdfs: 0,
      words: 0,
      totalSize: 0
    }
    
    files.value.forEach(file => {
      stats.totalSize += file.size
      
      switch (file.type) {
        case 'image':
          stats.images++
          break
        case 'pdf':
          stats.pdfs++
          break
        case 'word':
          stats.words++
          break
      }
    })
    
    return stats
  }

  // 工具函数
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type) => {
    const icons = {
      image: 'Picture',
      pdf: 'Document',
      word: 'Document',
      folder: 'Folder'
    }
    
    return icons[type] || 'Document'
  }

  // 导出状态和方法
  return {
    // 状态
    files,
    folders,
    fileTree,
    selectedFile,
    currentFolder,
    loading,
    error,

    // 计算属性
    currentFolderFiles,
    currentFolderSubfolders,

    // 方法
    initializeData,
    loadFiles,
    addFile,
    addFolder,
    deleteFile,
    deleteFolder,
    selectFile,
    setCurrentFolder,
    moveFile,
    renameFile,
    renameFolder,
    getFileById,
    getFolderById,
    getFilesByType,
    searchFiles,
    getFileStats,
    formatFileSize,
    getFileIcon
  }
})

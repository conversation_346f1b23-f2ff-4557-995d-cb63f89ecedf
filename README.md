# 水印系统

一个基于 Vue.js + Element Plus + .NET Core WebAPI + EF Core 的水印系统，支持图片、PDF 和 Word 文档的水印操作。

## 功能特性

### 用户管理
- **两级用户权限**：管理员和普通用户
- **权限控制**：
  - 管理员：拥有所有权限，可以管理用户、删除文件、下载源文件
  - 普通用户：基础权限，可通过管理员授权获得删除文件和下载源文件权限
- **用户认证**：基于 JWT 的安全认证系统

### 文件管理
- **文件上传**：支持 JPG、PNG、GIF、BMP、WebP、PDF、DOC、DOCX 格式
- **文件夹管理**：支持创建、删除文件夹，文件分类管理
- **权限控制**：根据用户权限控制文件删除和源文件下载

### 水印功能
- **图片水印**：支持对图片添加文字和图片水印
- **PDF水印**：支持对PDF文档添加水印
- **Word水印**：支持Word文档水印处理，可转换为PDF
- **水印定制**：可调整位置、大小、透明度、旋转角度等

### 统计记录
- 文件上传统计
- 水印导出统计
- 用户操作记录

## 技术栈

### 后端
- **.NET Core 8.0**：Web API 框架
- **Entity Framework Core**：ORM 框架
- **MySQL**：数据库
- **JWT Bearer**：身份认证
- **BCrypt.Net**：密码加密

### 前端
- **Vue 3**：前端框架
- **Element Plus**：UI 组件库
- **Pinia**：状态管理
- **Vue Router**：路由管理
- **Axios**：HTTP 客户端

## 快速开始

### 环境要求
- .NET 8.0 SDK
- Node.js 20.19.0+
- MySQL 5.7+

### 后端启动

1. 进入后端目录：
```bash
cd api/WatermarkCore
```

2. 配置数据库连接（修改 `appsettings.json`）：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Port=3306;Database=WatermarkSys;Uid=root;Pwd=your_password;CharSet=utf8mb4;"
  }
}
```

3. 运行项目：
```bash
dotnet run
```

后端将在 `http://localhost:5072` 启动。

### 前端启动

1. 进入前端目录：
```bash
cd client-vue
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm run dev
```

前端将在 `http://localhost:5173` 启动。

## 默认账户

系统会自动创建默认管理员账户：
- **用户名**：admin
- **密码**：admin123
- **角色**：管理员

首次登录后建议立即修改密码。

## 用户权限说明

### 管理员权限
- 创建和管理用户账户
- 设置用户权限（删除文件、下载源文件）
- 删除任何文件和文件夹
- 下载所有源文件
- 查看用户列表和统计信息

### 普通用户权限
- 上传文件到系统
- 创建和管理自己的文件夹
- 对文件添加水印
- 下载处理后的文件
- 修改自己的密码

### 可授权权限
管理员可以为普通用户授权以下权限：
- **删除文件权限**：允许删除文件和文件夹
- **下载源文件权限**：允许下载未处理的原始文件

## API 接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/change-password` - 修改密码
- `POST /api/auth/logout` - 用户登出

### 用户管理接口（管理员）
- `GET /api/auth/users` - 获取用户列表
- `PUT /api/auth/users/{id}/permissions` - 更新用户权限
- `DELETE /api/auth/users/{id}` - 删除用户

### 文件管理接口
- `GET /api/files` - 获取文件列表
- `POST /api/files/upload` - 上传文件
- `GET /api/files/{id}/download` - 下载文件（需要权限）
- `DELETE /api/files/{id}` - 删除文件（需要权限）

### 文件夹管理接口
- `GET /api/folders` - 获取文件夹列表
- `POST /api/folders` - 创建文件夹
- `PUT /api/folders/{id}` - 更新文件夹
- `DELETE /api/folders/{id}` - 删除文件夹（需要权限）

## 开发说明

### 项目结构
```
WatermarkYSys/
├── api/                    # 后端项目
│   └── WatermarkCore/      # .NET Core Web API
│       ├── Controllers/    # 控制器
│       ├── Models/         # 数据模型
│       ├── Services/       # 业务服务
│       ├── Data/          # 数据访问
│       └── Middleware/    # 中间件
├── client-vue/            # 前端项目
│   └── src/
│       ├── components/    # Vue 组件
│       ├── views/         # 页面组件
│       ├── stores/        # Pinia 状态管理
│       ├── services/      # API 服务
│       └── router/        # 路由配置
└── README.md
```

### 数据库设计
- **Users**：用户表，存储用户信息和权限
- **Folders**：文件夹表，支持层级结构
- **Files**：文件表，存储文件信息和元数据

### 安全特性
- JWT 令牌认证
- 密码 BCrypt 加密
- 基于角色的权限控制
- API 接口权限验证
- 文件上传安全检查

## 许可证

本项目采用 MIT 许可证。

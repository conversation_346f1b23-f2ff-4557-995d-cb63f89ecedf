<template>
  <el-dialog v-model="visible" title="使用帮助" width="600px">
    <div class="help-content">
      <el-collapse v-model="activeNames">
        <el-collapse-item title="支持的文件格式" name="formats">
          <div class="help-section">
            <h4>图片文件</h4>
            <p>支持: JPG, JPEG, PNG, GIF, BMP, WebP</p>
            <p>特点: 可实时预览水印效果，处理速度快</p>

            <h4>PDF文件</h4>
            <p>支持: PDF格式文档</p>
            <p>特点: 支持多页面水印，可处理大部分PDF文件</p>

            <h4>Word文档</h4>
            <p>支持: DOC, DOCX格式</p>
            <p>特点: 基础水印功能，适用于简单文档</p>
          </div>
        </el-collapse-item>

        <el-collapse-item title="PDF加密文件处理" name="pdf-encryption">
          <div class="help-section">
            <h4>什么是加密PDF？</h4>
            <p>加密PDF是设置了密码保护的PDF文件，通常用于保护文档内容不被未授权访问。</p>

            <h4>如何处理加密PDF？</h4>
            <ol>
              <li>
                <strong>推荐方法</strong>：先解除PDF密码保护
                <ul>
                  <li>使用Adobe Acrobat或其他PDF编辑器</li>
                  <li>输入密码后另存为新的PDF文件</li>
                  <li>上传解密后的PDF文件</li>
                </ul>
              </li>
              <li>
                <strong>直接处理</strong>：系统会尝试忽略加密处理
                <ul>
                  <li>可能会显示警告信息</li>
                  <li>水印效果可能受到影响</li>
                  <li>建议先测试小文件</li>
                </ul>
              </li>
            </ol>

            <h4>常见问题</h4>
            <p><strong>Q: 为什么加密PDF处理失败？</strong></p>
            <p>A: 某些高级加密的PDF文件无法直接处理，建议先解除密码保护。</p>

            <p><strong>Q: 处理后的PDF还有密码吗？</strong></p>
            <p>A: 处理后的PDF文件不会保留原始密码保护。</p>
          </div>
        </el-collapse-item>

        <el-collapse-item title="文件大小限制" name="file-size">
          <div class="help-section">
            <h4>大小限制</h4>
            <p>单个文件最大支持: <strong>50MB</strong></p>

            <h4>性能建议</h4>
            <ul>
              <li>图片文件: 建议不超过10MB，处理速度更快</li>
              <li>PDF文件: 建议不超过20MB，避免浏览器卡顿</li>
              <li>Word文档: 建议不超过5MB，确保兼容性</li>
            </ul>

            <h4>如果文件过大怎么办？</h4>
            <ul>
              <li>压缩图片质量</li>
              <li>分割大型PDF文档</li>
              <li>删除Word文档中的多余内容</li>
            </ul>
          </div>
        </el-collapse-item>

        <el-collapse-item title="水印设置技巧" name="watermark-tips">
          <div class="help-section">
            <h4>文字水印</h4>
            <ul>
              <li>建议透明度设置在0.3-0.7之间</li>
              <li>字体大小根据文档大小调整</li>
              <li>深色文档使用浅色水印，浅色文档使用深色水印</li>
              <li><strong>中文字符</strong>：PDF文字水印会自动转换为图片格式以确保正确显示</li>
              <li><strong>英文字符</strong>：直接使用PDF原生文字，文件大小更小</li>
            </ul>

            <h4>图片水印</h4>
            <ul>
              <li>推荐使用PNG格式的透明背景图片</li>
              <li>水印图片尺寸不宜过大</li>
              <li>建议使用简单的logo或标识</li>
            </ul>

            <h4>位置选择</h4>
            <ul>
              <li>重要文档建议使用居中位置</li>
              <li>不影响阅读的位置如右下角</li>
              <li>可以尝试不同角度的旋转效果</li>
            </ul>
          </div>
        </el-collapse-item>

        <el-collapse-item title="中文字符处理" name="chinese-support">
          <div class="help-section">
            <h4>为什么需要特殊处理中文字符？</h4>
            <p>PDF标准字体（如Helvetica、Times等）不包含中文字符，直接使用会导致编码错误。</p>

            <h4>系统如何处理中文字符？</h4>
            <ol>
              <li><strong>自动检测</strong>：系统会自动检测水印文字中是否包含中文字符</li>
              <li><strong>智能转换</strong>：对于中文字符，自动转换为图片水印</li>
              <li><strong>保持效果</strong>：转换后的图片水印保持原有的样式设置</li>
              <li><strong>无缝体验</strong>：用户无需手动操作，系统自动处理</li>
            </ol>

            <h4>中文水印的优势</h4>
            <ul>
              <li>支持所有中文字符，包括繁体字</li>
              <li>保持字体样式和颜色</li>
              <li>支持旋转和透明度设置</li>
              <li>兼容性好，在所有PDF阅读器中正确显示</li>
            </ul>

            <h4>注意事项</h4>
            <ul>
              <li>中文水印会略微增加PDF文件大小</li>
              <li>处理时间可能比英文水印稍长</li>
              <li>建议使用常见的中文字符以确保最佳效果</li>
            </ul>
          </div>
        </el-collapse-item>

        <el-collapse-item title="常见问题" name="faq">
          <div class="help-section">
            <h4>处理失败怎么办？</h4>
            <ul>
              <li>检查文件格式是否支持</li>
              <li>确认文件没有损坏</li>
              <li>尝试重新上传文件</li>
              <li>检查浏览器控制台错误信息</li>
            </ul>

            <h4>水印效果不理想？</h4>
            <ul>
              <li>调整透明度和大小</li>
              <li>更换水印位置</li>
              <li>尝试不同的颜色</li>
              <li>使用更合适的水印图片</li>
            </ul>

            <h4>浏览器兼容性</h4>
            <p>推荐使用最新版本的Chrome、Firefox、Safari或Edge浏览器。</p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

const visible = defineModel('visible', { type: Boolean, default: false })
const activeNames = ref(['formats'])
</script>

<style scoped>
.help-content {
  max-height: 500px;
  overflow-y: auto;
}

.help-section {
  padding: 16px 0;
}

.help-section h4 {
  color: #409eff;
  margin: 16px 0 8px 0;
  font-size: 14px;
}

.help-section p {
  margin: 8px 0;
  line-height: 1.6;
  color: #606266;
}

.help-section ul,
.help-section ol {
  margin: 8px 0;
  padding-left: 20px;
}

.help-section li {
  margin: 4px 0;
  line-height: 1.5;
  color: #606266;
}

.help-section strong {
  color: #303133;
}
</style>
